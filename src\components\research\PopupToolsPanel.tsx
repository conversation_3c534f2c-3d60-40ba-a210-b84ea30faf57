import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Replace, 
  Plus, 
  Eye, 
  Loader2,
  Settings,
  Type,
  Target,
  BookOpen,
  Sparkles,
  FlaskConical,
  Lightbulb,
  Search,
  TrendingUp,
  CheckCircle,
  FileText,
  BarChart3,
  FileCheck,
  Edit,
  PenTool,
  AlignLeft,
  List,
  Repeat
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, RESEARCH_TOOLS, ResearchTool } from './paper-generator/enhanced-ai.service';
import './academic-interface.css';

interface PopupToolsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onToolExecute: (toolId: string, mode: 'replace' | 'cursor' | 'popup') => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
}

export function PopupToolsPanel({
  isOpen,
  onClose,
  onToolExecute,
  selectedText,
  documentContent,
  aiLoading
}: PopupToolsPanelProps) {
  const [defaultAction, setDefaultAction] = useState<'replace' | 'cursor' | 'popup'>('popup');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [processingTool, setProcessingTool] = useState<string | null>(null);

  if (!isOpen) return null;

  // Action mode options
  const actionModes = [
    { id: 'replace', label: 'Replace Selected', icon: <Replace className="h-4 w-4" />, disabled: !selectedText.trim() },
    { id: 'cursor', label: 'Add to Cursor', icon: <Plus className="h-4 w-4" /> },
    { id: 'popup', label: 'Display in Popup', icon: <Eye className="h-4 w-4" /> }
  ];

  // Tool categories
  const toolCategories = [
    { id: 'all', name: 'All Tools' },
    { id: 'generation', name: 'Content Generation' },
    { id: 'enhancement', name: 'Text Enhancement' },
    { id: 'analysis', name: 'Analysis & Review' }
  ];

  // Icon mapping for tools
  const iconMap: Record<string, React.ReactNode> = {
    Type: <Type className="h-4 w-4" />,
    Target: <Target className="h-4 w-4" />,
    BookOpen: <BookOpen className="h-4 w-4" />,
    Sparkles: <Sparkles className="h-4 w-4" />,
    FlaskConical: <FlaskConical className="h-4 w-4" />,
    Lightbulb: <Lightbulb className="h-4 w-4" />,
    Search: <Search className="h-4 w-4" />,
    TrendingUp: <TrendingUp className="h-4 w-4" />,
    CheckCircle: <CheckCircle className="h-4 w-4" />,
    FileText: <FileText className="h-4 w-4" />,
    BarChart3: <BarChart3 className="h-4 w-4" />,
    FileCheck: <FileCheck className="h-4 w-4" />,
    Edit: <Edit className="h-4 w-4" />,
    PenTool: <PenTool className="h-4 w-4" />,
    AlignLeft: <AlignLeft className="h-4 w-4" />,
    List: <List className="h-4 w-4" />,
    Repeat: <Repeat className="h-4 w-4" />
  };

  // Get tools by category
  const getToolsByCategory = (category: string): ResearchTool[] => {
    if (category === 'all') return RESEARCH_TOOLS;
    return RESEARCH_TOOLS.filter(tool => tool.category === category);
  };

  // Handle tool execution
  const handleToolExecution = async (tool: ResearchTool) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }

    if (aiLoading || processingTool) return;

    setProcessingTool(tool.id);

    try {
      const context = tool.requiresSelection ? selectedText : documentContent;
      
      // Execute the tool
      await enhancedAIService.executeResearchTool(tool.id, context);
      
      // Use the default action mode
      onToolExecute(tool.id, defaultAction);
      
      toast.success(`${tool.name} completed successfully`);
      
      // Close popup if not displaying in popup
      if (defaultAction !== 'popup') {
        onClose();
      }
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    } finally {
      setProcessingTool(null);
    }
  };

  // Get color classes for tool categories
  const getCategoryColor = (category: string) => {
    const colors = {
      generation: 'bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-200',
      enhancement: 'bg-green-100 hover:bg-green-200 text-green-700 border-green-200',
      analysis: 'bg-purple-100 hover:bg-purple-200 text-purple-700 border-purple-200',
      review: 'bg-orange-100 hover:bg-orange-200 text-orange-700 border-orange-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-200';
  };

  const filteredTools = getToolsByCategory(selectedCategory);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <Card className="popup-panel w-full max-w-4xl max-h-[85vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="popup-panel-header flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Settings className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Research Tools</h2>
              <p className="text-sm text-gray-600">AI-powered tools for academic writing</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Category Filter */}
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Tool Category
              </label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {toolCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Default Action */}
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Default Action
              </label>
              <Select value={defaultAction} onValueChange={(value) => setDefaultAction(value as 'replace' | 'cursor' | 'popup')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  {actionModes.map((mode) => (
                    <SelectItem key={mode.id} value={mode.id} disabled={mode.disabled}>
                      <div className="flex items-center gap-2">
                        {mode.icon}
                        {mode.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedText && (
            <div className="mt-3">
              <Badge variant="secondary" className="text-xs">
                {selectedText.length} characters selected
              </Badge>
            </div>
          )}
        </div>

        {/* Tools Grid */}
        <ScrollArea className="flex-1 p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTools.map((tool) => (
              <Button
                key={tool.id}
                variant="outline"
                className={`h-auto p-4 flex flex-col items-start gap-3 text-left transition-all duration-200 tool-button ${getCategoryColor(tool.category)}`}
                onClick={() => handleToolExecution(tool)}
                disabled={aiLoading || processingTool !== null || (tool.requiresSelection && !selectedText.trim())}
                title={tool.description}
              >
                <div className="flex items-center gap-2 w-full">
                  {processingTool === tool.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    iconMap[tool.icon] || <FileText className="h-4 w-4" />
                  )}
                  <span className="font-medium text-sm">{tool.name}</span>
                  {tool.requiresSelection && (
                    <Badge variant="outline" className="text-xs ml-auto">
                      Selection
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-600 leading-relaxed">
                  {tool.description}
                </p>
              </Button>
            ))}
          </div>

          {filteredTools.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No tools found</h3>
              <p className="text-gray-600">Try selecting a different category.</p>
            </div>
          )}
        </ScrollArea>
      </Card>
    </div>
  );
}
